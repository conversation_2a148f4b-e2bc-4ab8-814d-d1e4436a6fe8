<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;
use InvalidArgumentException;

/**
 * Class ProyectoModulo
 * Represents a module associated with a project.
 */
class ProyectoModulo
{
	// --- Attributes ---
	private ?int    $id             = null;
	private ?int    $id_proyecto    = null;
	private ?string $nombre_proyecto = null; // Added attribute to store project name
	private ?string $descripcion    = null;
	private ?int    $estado         = null;

	/**
	 * Constructor: Initializes properties with default values.
	 */
	public function __construct()
	{
		$this->id              = null;
		$this->id_proyecto     = null;
		$this->nombre_proyecto = null; // Initialize new attribute
		$this->descripcion     = null;
		$this->estado          = 1; // Default to active
	}

	/**
	 * Static method to construct a ProyectoModulo object from an array (e.g., DB row).
	 *
	 * @param array $resultado Associative array with module data.
	 *
	 * @return self ProyectoModulo instance.
	 * @throws Exception If an error occurs during construction.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                  = new self();
			$objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : null;
			$objeto->id_proyecto     = isset($resultado['id_proyecto']) ? (int)$resultado['id_proyecto'] : null;
			$objeto->nombre_proyecto = $resultado['nombre_proyecto'] ?? null; // Set the project name
			$objeto->descripcion     = $resultado['descripcion'] ?? null;
			$objeto->estado          = isset($resultado['estado']) ? (int)$resultado['estado'] : 1;

			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir ProyectoModulo: " . $e->getMessage());
		}
	}

	// --- Getters and Setters ---

	/**
	 * Get the module ID.
	 *
	 * @return int|null The module ID.
	 */
	public function getId(): ?int
	{
		return $this->id;
	}

	/**
	 * Set the module ID.
	 *
	 * @param int|null $id The module ID.
	 *
	 * @return self For method chaining.
	 */
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	/**
	 * Get the project ID.
	 *
	 * @return int|null The project ID.
	 */
	public function getIdProyecto(): ?int
	{
		return $this->id_proyecto;
	}

	/**
	 * Set the project ID.
	 *
	 * @param int|null $id_proyecto The project ID.
	 *
	 * @return self For method chaining.
	 */
	public function setIdProyecto(?int $id_proyecto): self
	{
		$this->id_proyecto = $id_proyecto;
		return $this;
	}

	/**
	 * Get the project name.
	 *
	 * @return string|null The project name.
	 */
	public function getNombreProyecto(): ?string
	{
		return $this->nombre_proyecto;
	}

	/**
	 * Set the project name.
	 *
	 * @param string|null $nombre_proyecto The project name.
	 *
	 * @return self For method chaining.
	 */
	public function setNombreProyecto(?string $nombre_proyecto): self
	{
		$this->nombre_proyecto = $nombre_proyecto;
		return $this;
	}

	/**
	 * Get the module description.
	 *
	 * @return string|null The module description.
	 */
	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	/**
	 * Set the module description.
	 *
	 * @param string|null $descripcion The module description.
	 *
	 * @return self For method chaining.
	 */
	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	/**
	 * Get the module status.
	 *
	 * @return int|null The module status (1=active, 0=inactive).
	 */
	public function getEstado(): ?int
	{
		return $this->estado;
	}

	/**
	 * Set the module status.
	 *
	 * @param int|null $estado The module status (1=active, 0=inactive).
	 *
	 * @return self For method chaining.
	 */
	public function setEstado(?int $estado): self
	{
		$this->estado = $estado;
		return $this;
	}

	// --- Data Access Methods ---

	/**
	 * Get a module by its ID.
	 *
	 * @param int $id       The module ID.
	 * @param PDO $conexion PDO connection.
	 *
	 * @return self|null ProyectoModulo object or null if not found.
	 * @throws Exception If there's a DB error or invalid ID.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		if ($id <= 0) {
			throw new InvalidArgumentException("ID de Módulo inválido.");
		}

		try {
			$query = <<<SQL
			SELECT pm.*, p.descripcion AS nombre_proyecto
			FROM proyectos_modulos pm
			LEFT JOIN proyectos p ON pm.id_proyecto = p.id
			WHERE pm.id = :id LIMIT 1
			SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;
		} catch (PDOException $e) {
			throw new Exception("Error al obtener Módulo (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Get a list of modules.
	 *
	 * @param PDO  $conexion    PDO connection.
	 * @param bool $soloActivos If true, only returns active modules.
	 *
	 * @return array Array of ProyectoModulo objects.
	 * @throws Exception If there's a DB error.
	 */
	public static function get_list(PDO $conexion, bool $soloActivos = true): array
	{
		try {
			$query = <<<SQL
			SELECT pm.*, p.descripcion AS nombre_proyecto
			FROM proyectos_modulos pm
			LEFT JOIN proyectos p ON pm.id_proyecto = p.id
			SQL;

			if ($soloActivos) {
				$query .= " WHERE pm.estado = 1";
			}

			$query .= " ORDER BY pm.id_proyecto, pm.id";

			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}

			return $listado;
		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Módulos: " . $e->getMessage());
		}
	}

	/**
	 * Get modules by project ID.
	 *
	 * @param int  $id_proyecto The project ID.
	 * @param PDO  $conexion    PDO connection.
	 * @param bool $soloActivos If true, only returns active modules.
	 *
	 * @return array Array of ProyectoModulo objects.
	 * @throws Exception If there's a DB error.
	 */
	public static function getByProyecto(int $id_proyecto, PDO $conexion, bool $soloActivos = true): array
	{
		try {
			$query = <<<SQL
			SELECT
				 pm.*
				,p.descripcion AS nombre_proyecto
			FROM proyectos_modulos pm
			LEFT JOIN proyectos p ON pm.id_proyecto = p.id
			WHERE
				pm.id_proyecto = :id_proyecto
			SQL;

			if ($soloActivos) {
				$query .= " AND pm.estado = 1";
			}

			$query .= " ORDER BY pm.descripcion";

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_proyecto", $id_proyecto, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}

			return $listado;
		} catch (PDOException $e) {
			throw new Exception("Error al obtener Módulos por Proyecto (ID Proyecto: $id_proyecto): " . $e->getMessage());
		}
	}

	/**
	 * Search modules by project ID and description.
	 *
	 * @param int    $id_proyecto   The project ID.
	 * @param string $search_query  The search query for module description.
	 * @param int    $limit         Maximum number of results to return.
	 * @param PDO    $conexion      PDO connection.
	 * @param bool   $soloActivos   If true, only returns active modules.
	 *
	 * @return array Array of ProyectoModulo objects.
	 * @throws Exception If there's a DB error.
	 */
	public static function searchByProyecto(int $id_proyecto, string $search_query, int $limit, PDO $conexion, bool $soloActivos = true): array
	{
		try {
			$query = <<<SQL
			SELECT
				 pm.*
				,p.descripcion AS nombre_proyecto
			FROM proyectos_modulos pm
			LEFT JOIN proyectos p ON pm.id_proyecto = p.id
			WHERE
				pm.id_proyecto = :id_proyecto
				AND pm.descripcion LIKE :search_query
			SQL;

			if ($soloActivos) {
				$query .= " AND pm.estado = 1";
			}

			$query .= " ORDER BY pm.descripcion LIMIT :limit";

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_proyecto", $id_proyecto, PDO::PARAM_INT);
			$statement->bindValue(":search_query", '%' . trim($search_query) . '%', PDO::PARAM_STR);
			$statement->bindValue(":limit", $limit, PDO::PARAM_INT);
			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}

			return $listado;
		} catch (PDOException $e) {
			throw new Exception("Error al buscar Módulos por Proyecto (ID Proyecto: $id_proyecto): " . $e->getMessage());
		}
	}

	/**
	 * Validate required data before saving.
	 *
	 * @throws Exception If required data is missing.
	 */
	private function validarDatosObligatorios(): void
	{
		if ($this->id_proyecto === null || $this->id_proyecto <= 0) {
			throw new Exception("El ID del proyecto es obligatorio.");
		}

		if ($this->descripcion === null || trim($this->descripcion) === '') {
			throw new Exception("La descripción del módulo es obligatoria.");
		}
	}

	/**
	 * Save (insert or update) the module to the database.
	 *
	 * @param PDO $conexion PDO connection.
	 *
	 * @return bool True if successful, False otherwise.
	 * @throws Exception If required data is missing or there's a DB error.
	 */
	public function guardar(PDO $conexion): bool
	{
		$this->validarDatosObligatorios();

		try {
			if ($this->getId() !== null && $this->getId() > 0) {
				return $this->_update($conexion);
			} else {
				$newId = $this->_insert($conexion);
				if ($newId !== false) {
					$this->setId($newId);
					return true;
				}
				return false;
			}
		} catch (PDOException $e) {
			throw new Exception("Error al guardar Módulo: " . $e->getMessage());
		}
	}

	/**
	 * Insert a new module into the database. (Private method)
	 *
	 * @param PDO $conexion PDO connection.
	 *
	 * @return int|false The ID of the new module or false on failure.
	 * @throws PDOException On database error.
	 */
	private function _insert(PDO $conexion): int|false
	{
		$query = <<<SQL
        INSERT INTO proyectos_modulos (id_proyecto, descripcion, estado)
        VALUES (:id_proyecto, :descripcion, :estado)
        SQL;

		$statement = $conexion->prepare($query);
		$statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
		$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
		$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);

		$success = $statement->execute();

		return $success ? (int)$conexion->lastInsertId() : false;
	}

	/**
	 * Update an existing module in the database. (Private method)
	 *
	 * @param PDO $conexion PDO connection.
	 *
	 * @return bool True on success, false on failure.
	 * @throws PDOException On database error.
	 */
	private function _update(PDO $conexion): bool
	{
		$query = <<<SQL
        UPDATE proyectos_modulos SET
            id_proyecto = :id_proyecto,
            descripcion = :descripcion,
            estado = :estado
        WHERE id = :id
        SQL;

		$statement = $conexion->prepare($query);
		$statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
		$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
		$statement->bindValue(':estado', $this->getEstado() ?? 1, PDO::PARAM_INT);
		$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

		return $statement->execute();
	}

	/**
	 * Change the status of a module.
	 *
	 * @param PDO $conexion    PDO connection.
	 * @param int $nuevoEstado The new status (1=active, 0=inactive).
	 *
	 * @return bool True if successful, False otherwise.
	 * @throws Exception If there's a DB error or the module doesn't exist.
	 */
	public function cambiarEstado(PDO $conexion, int $nuevoEstado): bool
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("No se puede cambiar el estado de un módulo que no existe en la base de datos.");
		}

		try {
			$query     = "UPDATE proyectos_modulos SET estado = :estado WHERE id = :id";
			$statement = $conexion->prepare($query);
			$statement->bindValue(':estado', $nuevoEstado, PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			$success = $statement->execute();
			if ($success) {
				$this->estado = $nuevoEstado;
			}
			return $success;
		} catch (PDOException $e) {
			throw new Exception("Error al cambiar estado del Módulo (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Delete a module from the database.
	 *
	 * @param PDO $conexion PDO connection.
	 *
	 * @return bool True if successful, False otherwise.
	 * @throws Exception If there's a DB error or the module doesn't exist.
	 */
	public function eliminar(PDO $conexion): bool
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("No se puede eliminar un módulo que no existe en la base de datos.");
		}

		try {
			$query     = "DELETE FROM proyectos_modulos WHERE id = :id";
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			return $statement->execute();
		} catch (PDOException $e) {
			throw new Exception("Error al eliminar Módulo (ID: {$this->getId()}): " . $e->getMessage());
		}
	}
}
