<?php

// Iniciar sesión si es necesario
use App\classes\Proyecto;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region region init variables
$proyectos = []; // Initialize as an empty array
#endregion init variables
#region region Handle Flash Message Success
// Check for a success flash message from session
if (!empty($_SESSION['flash_message_success'])) {
	$success_text    = $_SESSION['flash_message_success'];
	$success_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success
#region region Handle Flash Message Error
// Check for an error flash message from session
if (!empty($_SESSION['flash_message_error'])) {
	$error_text    = $_SESSION['flash_message_error'];
	$error_display = 'show';
	// Clear the flash message so it doesn't show again
	unset($_SESSION['flash_message_error']);
}
#region region Modify proyecto

#region region Handle GET Actions (Ver Tareas)
if ($_SERVER['REQUEST_METHOD'] == 'GET' && isset($_GET['action']) && $_GET['action'] == 'ver_tareas') {
	$proyectoId = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);

	if ($proyectoId) {
		// Clear any existing project filter session data
		unset($_SESSION['filtro_proyecto_id']);

		// Store the project ID in session for secure parameter passing
		$_SESSION['filtro_proyecto_id'] = $proyectoId;

		// Redirect to tasks view
		header('Location: ltareas');
		exit;
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de proyecto inválido.";
		header('Location: lproyectos');
		exit;
	}
}
#endregion Handle GET Actions

#region region Handle POST Actions (Deactivation)
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action']) && $_POST['action'] == 'desactivar') {
	$proyectoIdToDeactivate = filter_input(INPUT_POST, 'proyectoId', FILTER_VALIDATE_INT);

	if ($proyectoIdToDeactivate) {
		try {
			$success = Proyecto::desactivar($proyectoIdToDeactivate, $conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Proyecto desactivado correctamente.";
			} else {
				// This case might happen if the ID doesn't exist, though desactivar doesn't explicitly check
				$_SESSION['flash_message_error'] = "Error: No se pudo encontrar o desactivar el proyecto.";
				// Consider setting an error type for the flash message if your system supports it
			}
		} catch (Exception $e) {
			// Log the detailed error: error_log("Error desactivando proyecto ID $proyectoIdToDeactivate: " . $e->getMessage());
			$_SESSION['flash_message_error'] = "Error al desactivar proyecto: " . $e->getMessage();
			// Consider setting an error type for the flash message
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de proyecto inválido para desactivar.";
		// Consider setting an error type
	}

	// Redirect back to the proyecto list page after processing
	header('Location: lproyectos');
	exit;
}
#endregion Handle POST Actions

#region try
try {
	$proyectos = Proyecto::get_list($conexion);

} catch (PDOException $e) {
	// Specific handling for database errors
	// Log the error: error_log("Database error fetching proyectos: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Error de base de datos al obtener la lista de proyectos.";
} catch (Exception $e) {
	// General error handling
	// Log the error: error_log("Error fetching proyectos: " . $e->getMessage());
	$error_display = 'show';
	$error_text    = "Ocurrió un error inesperado al obtener la lista de proyectos: " . $e->getMessage();
}

#endregion try

require_once __ROOT__ . '/views/admin/lproyectos.view.php';

?>
