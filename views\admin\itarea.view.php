<?php
#region region DOCS
/** @var bool $is_edit_mode */
/** @var Tarea|null $tarea */
/** @var string $descripcion */
/** @var int $id_tarea_estado */
/** @var int|null $id_proyecto */
/** @var int|null $id_proyecto_modulo */
/** @var int|null $id_tarea_padre */
/** @var array $proyectos */
/** @var array $modulos */
/** @var array $tareas_padre */
/** @var array $agentes */
/** @var array $tarea_agentes */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Tarea;
use App\classes\Proyecto;
use App\classes\ProyectoModulo;
use App\classes\Agente;
use App\classes\TareaAgente;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | <?php echo $is_edit_mode ? 'Editar' : 'Crear'; ?> Tarea</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<style>
		/* Custom styles for disabled select */
		select:disabled {
			background-color: #f8f9fa;
			border-color: #dee2e6;
			color: #6c757d;
			cursor: not-allowed;
		}

		/* Style for input group with disabled select */
		.input-group select:disabled + button.btn-outline-success {
			background-color: #f8f9fa;
			border-color: #28a745;
			color: #28a745;
			opacity: 0.65;
		}

		/* Autocomplete styles */
		.ui-autocomplete {
			max-height: 300px;
			overflow-y: auto;
			background: var(--bs-body-bg);
			border: 1px solid var(--bs-border-color);
			border-radius: 0.375rem;
			box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
			z-index: 1050;
		}

		.ui-autocomplete .ui-menu-item {
			padding: 0;
			margin: 0;
			border-bottom: 1px solid var(--bs-border-color);
		}

		.ui-autocomplete .ui-menu-item:last-child {
			border-bottom: none;
		}

		.ui-autocomplete .ui-menu-item-wrapper {
			padding: 0.75rem 1rem;
			cursor: pointer;
			color: var(--bs-body-color);
			text-decoration: none;
			display: block;
		}

		.ui-autocomplete .ui-menu-item-wrapper:hover,
		.ui-autocomplete .ui-state-active {
			background-color: var(--bs-primary);
			color: white;
		}

		.autocomplete-item .task-description {
			font-weight: 600;
			margin-bottom: 0.25rem;
			font-size: .9rem;
		}

		.autocomplete-item .project-name {
			font-size: 0.875rem;
			color: var(--bs-secondary);
		}

		.ui-state-active .autocomplete-item .project-name {
			color: rgba(255, 255, 255, 0.8);
		}

		.autocomplete-item .badge {
			font-size: 0.7rem;
			/*padding: 0.25rem 0.5rem;*/
		}

		/* Loading spinner positioning */
		.position-relative .spinner-border-sm {
			width: 1rem;
			height: 1rem;
		}
	</style>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<?php if ($is_edit_mode): ?>
					<h4 class="mb-0">Editar Tarea</h4>
					<p class="mb-0 text-muted">Modifique los detalles de la tarea existente.</p>
				<?php elseif (isset($id_tarea_padre) && $id_tarea_padre): ?>
					<h4 class="mb-0">Crear Tarea Hija</h4>
					<p class="mb-0 text-muted">Ingrese los detalles de la nueva tarea hija. La tarea padre ya está pre-seleccionada.</p>
				<?php else: ?>
					<h4 class="mb-0">Crear Nueva Tarea</h4>
					<p class="mb-0 text-muted">Ingrese los detalles de la nueva tarea.</p>
				<?php endif; ?>
			</div>
			<div class="ms-auto">
				<?php
				$proyecto_id_param = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
				if ($proyecto_id_param): ?>
					<a href="javascript:void(0);" onclick="redirectToTasksWithProject(<?php echo $proyecto_id_param; ?>)" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
				<?php else: ?>
					<a href="ltareas" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver a la Lista</a>
				<?php endif; ?>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region TABS ?>
		<ul class="nav nav-tabs nav-tabs-v2" id="tareaTab" role="tablist">
			<li class="nav-item me-2" role="presentation">
				<a href="#tarea-details-tab" class="nav-link active" id="tarea-details-link" data-bs-toggle="tab" role="tab" aria-controls="tarea-details-tab" aria-selected="true">
					<i class="fa fa-info-circle fa-fw me-1"></i> Detalles de la Tarea
				</a>
			</li>
			<?php if ($is_edit_mode && $tarea): ?>
			<li class="nav-item me-2" role="presentation">
				<a href="#tarea-agentes-tab" class="nav-link" id="tarea-agentes-link" data-bs-toggle="tab" role="tab" aria-controls="tarea-agentes-tab" aria-selected="false">
					<i class="fa fa-users fa-fw me-1"></i> Agentes Asignados
				</a>
			</li>
			<?php endif; ?>
		</ul>

		<div class="tab-content pt-3" id="tareaTabContent">

			<?php #region region TAB 1: TAREA DETAILS FORM ?>
			<div class="tab-pane fade show active" id="tarea-details-tab" role="tabpanel" aria-labelledby="tarea-details-link">
				<form action="itarea<?php echo $is_edit_mode ? '?id=' . $tarea->getId() : ''; ?>" method="POST" id="tarea-form" novalidate>
					<div class="panel panel-inverse no-border-radious mb-0">
						<div class="panel-heading no-border-radious">
							<h4 class="panel-title">
								<?php if ($is_edit_mode): ?>
									Editar Tarea
								<?php elseif (isset($id_tarea_padre) && $id_tarea_padre): ?>
									Crear Tarea Hija
								<?php else: ?>
									Crear Nueva Tarea
								<?php endif; ?>
							</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
							</div>
						</div>
						<div class="panel-body">
					<div class="row mb-3">
						<div class="col-md-12">
							<label for="descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="descripcion" name="descripcion" required value="<?php echo htmlspecialchars($descripcion); ?>">
							<div class="invalid-feedback" id="descripcion-error">
								La descripción es requerida.
							</div>
						</div>
					</div>

					<div class="row mb-3">
						<div class="col-md-6">
							<label for="id_tarea_estado" class="form-label">Estado <span class="text-danger">*</span></label>
							<select class="form-select" id="id_tarea_estado" name="id_tarea_estado" required>
								<option value="<?php echo Tarea::ESTADO_PENDIENTE; ?>" <?php echo $id_tarea_estado === Tarea::ESTADO_PENDIENTE ? 'selected' : ''; ?>>
									Pendiente
								</option>
								<option value="<?php echo Tarea::ESTADO_EN_PROGRESO; ?>" <?php echo $id_tarea_estado === Tarea::ESTADO_EN_PROGRESO ? 'selected' : ''; ?>>
									En Progreso
								</option>
							</select>
							<div class="invalid-feedback" id="id_tarea_estado-error">
								El estado es requerido.
							</div>
						</div>

						<?php if ($is_edit_mode && $tarea && $tarea->getFechaCreacion()): ?>
						<div class="col-md-6">
							<label class="form-label">Fecha de Creación</label>
							<input type="text" class="form-control" value="<?php
								$fecha_obj = new DateTime($tarea->getFechaCreacion());
								echo $fecha_obj->format('d/m/Y H:i');
							?>" readonly>
						</div>
						<?php endif; ?>
					</div>

					<div class="row mb-3">
						<div class="col-md-6">
							<label for="id_proyecto" class="form-label">Proyecto <span class="text-danger">*</span></label>
							<select class="form-select" id="id_proyecto" name="id_proyecto" required>
								<option value="">-- Seleccione un proyecto --</option>
								<?php foreach ($proyectos as $proyecto): ?>
								<option value="<?php echo $proyecto->getId(); ?>" <?php echo $id_proyecto == $proyecto->getId() ? 'selected' : ''; ?>>
									<?php echo htmlspecialchars($proyecto->getDescripcion()); ?>
								</option>
								<?php endforeach; ?>
							</select>
							<div class="invalid-feedback" id="id_proyecto-error">
								El proyecto es requerido.
							</div>
						</div>

						<div class="col-md-6">
							<label for="modulo_autocomplete" class="form-label">Módulo</label>
							<div class="input-group">
								<div class="position-relative flex-grow-1">
									<input type="text"
										   class="form-control"
										   id="modulo_autocomplete"
										   placeholder="Escriba para buscar un módulo..."
										   autocomplete="off"
										   <?php echo empty($id_proyecto) ? 'disabled' : ''; ?>
										   value="<?php
										   		if (isset($id_proyecto_modulo) && $id_proyecto_modulo) {
										   			foreach ($modulos as $modulo) {
										   				if ($modulo->getId() == $id_proyecto_modulo) {
										   					echo htmlspecialchars($modulo->getDescripcion());
										   					break;
										   				}
										   			}
										   		}
										   ?>">
									<input type="hidden" id="id_proyecto_modulo" name="id_proyecto_modulo" value="<?php echo htmlspecialchars($id_proyecto_modulo ?? ''); ?>">
									<div id="modulo-loading" class="position-absolute top-50 end-0 translate-middle-y me-3" style="display: none;">
										<div class="spinner-border spinner-border-sm text-primary" role="status">
											<span class="visually-hidden">Buscando...</span>
										</div>
									</div>
								</div>
								<button class="btn btn-outline-success" type="button" id="btn-nuevo-modulo" <?php echo empty($id_proyecto) ? 'disabled' : ''; ?> data-bs-toggle="modal" data-bs-target="#createModuloModal">
									<i class="fa fa-plus"></i>
								</button>
							</div>
							<div class="form-text">
								<i class="fa fa-info-circle"></i>
								<?php if (empty($id_proyecto)): ?>
									Seleccione un proyecto primero para buscar módulos.
								<?php else: ?>
									Escriba al menos 2 caracteres para buscar módulos del proyecto seleccionado.
								<?php endif; ?>
							</div>
							<div id="modulo-error" class="invalid-feedback" style="display: none;">
								Error al buscar módulos. Inténtelo de nuevo.
							</div>
						</div>
					</div>

						<div class="row mb-3">
							<div class="col-md-12">
								<label for="tarea_padre_autocomplete" class="form-label">Tarea Padre (Opcional)</label>
								<div class="position-relative">
									<input type="text"
										   class="form-control"
										   id="tarea_padre_autocomplete"
										   placeholder="Escriba para buscar una tarea padre..."
										   autocomplete="off"
										   value="<?php
										   		if (isset($id_tarea_padre) && $id_tarea_padre) {
										   			// First check if we have the parent_task from URL parameter
										   			if (isset($parent_task) && $parent_task && $parent_task->getId() == $id_tarea_padre) {
										   				echo htmlspecialchars($parent_task->getDescripcion());
										   				if ($parent_task->getNombreProyecto()) {
										   					echo ' (' . htmlspecialchars($parent_task->getNombreProyecto()) . ')';
										   				}
										   			} elseif (isset($tareas_padre)) {
										   				// Fallback to searching in tareas_padre array
										   				foreach ($tareas_padre as $tarea_padre) {
										   					if ($tarea_padre->getId() == $id_tarea_padre) {
										   						echo htmlspecialchars($tarea_padre->getDescripcion());
										   						if ($tarea_padre->getNombreProyecto()) {
										   							echo ' (' . htmlspecialchars($tarea_padre->getNombreProyecto()) . ')';
										   						}
										   						break;
										   					}
										   				}
										   			}
										   		}
										   ?>">
									<input type="hidden" id="id_tarea_padre" name="id_tarea_padre" value="<?php echo htmlspecialchars($id_tarea_padre ?? ''); ?>">
									<div id="tarea-padre-loading" class="position-absolute top-50 end-0 translate-middle-y me-3" style="display: none;">
										<div class="spinner-border spinner-border-sm text-primary" role="status">
											<span class="visually-hidden">Buscando...</span>
										</div>
									</div>
								</div>
								<div class="form-text">
									<i class="fa fa-info-circle"></i>
									<?php if (isset($id_tarea_padre) && $id_tarea_padre): ?>
										Tarea padre pre-seleccionada. Puede cambiarla escribiendo al menos 2 caracteres para buscar otra tarea padre.
									<?php else: ?>
										Escriba al menos 2 caracteres para buscar una tarea padre. Las tareas padre ayudan a organizar el trabajo de manera jerárquica.
									<?php endif; ?>
								</div>
								<div id="tarea-padre-error" class="invalid-feedback" style="display: none;">
									Error al buscar tareas. Inténtelo de nuevo.
								</div>
							</div>
						</div>

					<?php if ($is_edit_mode && $tarea && $tarea->getFechaTerminacion()): ?>
					<div class="row mb-3">
						<div class="col-md-6">
							<label class="form-label">Fecha de Terminación</label>
							<input type="text" class="form-control" value="<?php
								$fecha_obj = new DateTime($tarea->getFechaTerminacion());
								echo $fecha_obj->format('d/m/Y H:i');
							?>" readonly>
						</div>
					</div>
					<?php endif; ?>
						</div>
						<div class="panel-footer text-end">
							<button type="submit" class="btn btn-primary"><i class="fa fa-save fa-fw me-1"></i> <?php echo $is_edit_mode ? 'Actualizar' : 'Guardar'; ?> Tarea</button>
							<?php
							$proyecto_id_param = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
							if ($proyecto_id_param): ?>
								<a href="javascript:void(0);" onclick="redirectToTasksWithProject(<?php echo $proyecto_id_param; ?>)" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
							<?php else: ?>
								<a href="ltareas" class="btn btn-secondary"><i class="fa fa-times fa-fw me-1"></i> Cancelar</a>
							<?php endif; ?>
						</div>
					</div>
				</form>
			</div>
			<?php #endregion TAB 1: TAREA DETAILS FORM ?>

			<?php #region region TAB 2: TAREA AGENTES ?>
			<?php if ($is_edit_mode && $tarea): ?>
			<div class="tab-pane fade" id="tarea-agentes-tab" role="tabpanel" aria-labelledby="tarea-agentes-link">

				<?php #region ADD NEW AGENTE FORM ?>
				<div class="panel panel-inverse no-border-radious mb-3">
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">Asignar Nuevo Agente</h4>
						<div class="panel-heading-btn">
							<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
							<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
						</div>
					</div>
					<div class="panel-body">
						<form id="add-agente-form" novalidate>
							<div class="row">
								<div class="col-md-4 mb-3">
									<label for="id_agente" class="form-label">Agente <span class="text-danger">*</span></label>
									<select class="form-select" id="id_agente" name="id_agente" required>
										<option value="">-- Seleccione un agente --</option>
										<?php
										// Debug output
										if (empty($agentes)) {
											echo '<!-- DEBUG: $agentes array is empty -->';
										} else {
											echo '<!-- DEBUG: Found ' . count($agentes) . ' agents -->';
										}
										foreach ($agentes as $agente): ?>
										<option value="<?php echo $agente->getId(); ?>">
											<?php echo htmlspecialchars($agente->getDescripcion()); ?>
										</option>
										<?php endforeach; ?>
									</select>
									<div class="invalid-feedback">Debe seleccionar un agente.</div>
								</div>
								<div class="col-md-4 mb-3">
									<label for="costo_usd" class="form-label">Costo USD</label>
									<input type="number" class="form-control" id="costo_usd" name="costo_usd" step="0.01" min="0" placeholder="0.00">
									<div class="invalid-feedback">Ingrese un valor válido.</div>
								</div>
								<div class="col-md-4 mb-3">
									<label for="n_mensajes" class="form-label">N° Mensajes</label>
									<input type="number" class="form-control" id="n_mensajes" name="n_mensajes" min="0" value="0">
									<div class="invalid-feedback">Ingrese un número válido.</div>
								</div>
							</div>
							<div class="text-end">
								<button type="submit" class="btn btn-success"><i class="fa fa-plus fa-fw me-1"></i> Asignar Agente</button>
							</div>
						</form>
						<div id="add-agente-feedback" class="mt-3"></div>
					</div>
				</div>
				<?php #endregion ADD NEW AGENTE FORM ?>

				<?php #region EXISTING AGENTES TABLE ?>
				<div class="panel panel-inverse no-border-radious mb-0">
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">Agentes Asignados</h4>
					</div>
					<div class="panel-body p-0">
						<div class="table-responsive">
							<table class="table table-striped table-hover mb-0">
								<thead>
									<tr class="text-nowrap">
										<th class="text-center w-80px">Acciones</th>
										<th>Agente</th>
										<th class="text-end">Costo USD</th>
										<th class="text-center">N° Mensajes</th>
									</tr>
								</thead>
								<tbody id="agentes-table-body">
									<?php if (empty($tarea_agentes)): ?>
										<tr>
											<td colspan="4" class="text-center text-muted">No hay agentes asignados a esta tarea.</td>
										</tr>
									<?php else: ?>
										<?php foreach ($tarea_agentes as $tarea_agente): ?>
										<tr data-id="<?php echo $tarea_agente->getId(); ?>">
											<td class="text-center">
												<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-agente"
												        title="Editar"
												        data-agente-id="<?php echo $tarea_agente->getId(); ?>">
													<i class="fa fa-edit"></i>
												</button>
												<button type="button" class="btn btn-xs btn-danger btn-delete-agente"
												        title="Eliminar"
												        data-agente-id="<?php echo $tarea_agente->getId(); ?>"
												        data-agente-nombre="<?php echo htmlspecialchars($tarea_agente->getAgenteDescripcion() ?? 'N/A'); ?>">
													<i class="fa fa-trash-alt"></i>
												</button>
											</td>
											<td class="agente-nombre"><?php echo htmlspecialchars($tarea_agente->getAgenteDescripcion() ?? 'N/A'); ?></td>
											<td class="text-end costo-usd">$<?php echo number_format($tarea_agente->getCostoUsd() ?? 0, 2); ?></td>
											<td class="text-center n-mensajes"><?php echo $tarea_agente->getNMensajes() ?? 0; ?></td>
										</tr>
										<?php endforeach; ?>
									<?php endif; ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
				<?php #endregion EXISTING AGENTES TABLE ?>

			</div>
			<?php endif; ?>
			<?php #endregion TAB 2: TAREA AGENTES ?>

		</div>
		<?php #endregion TABS ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<!-- Create Modulo Modal -->
<div class="modal fade" id="createModuloModal" tabindex="-1" aria-labelledby="createModuloModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<form id="create-modulo-form">
				<div class="modal-header">
					<h5 class="modal-title" id="createModuloModalLabel">Crear Nuevo Módulo</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div class="mb-3">
						<label for="nuevo_modulo_descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
						<input type="text" class="form-control" id="nuevo_modulo_descripcion" name="descripcion" required>
					</div>
					<div id="nuevo-modulo-error" class="alert alert-danger" style="display: none;"></div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="submit" class="btn btn-primary">Guardar</button>
				</div>
			</form>
		</div>
	</div>
</div>

<!-- Edit TareaAgente Modal -->
<?php if ($is_edit_mode && $tarea): ?>
<div class="modal fade" id="editAgenteModal" tabindex="-1" aria-labelledby="editAgenteModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content">
			<form id="edit-agente-form">
				<div class="modal-header">
					<h5 class="modal-title" id="editAgenteModalLabel">Editar Asignación de Agente</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<input type="hidden" id="edit-tarea-agente-id" name="id">
					<div class="mb-3">
						<label for="edit_id_agente" class="form-label">Agente <span class="text-danger">*</span></label>
						<select class="form-select" id="edit_id_agente" name="id_agente" required>
							<option value="">-- Seleccione un agente --</option>
							<?php
							// Debug output for edit modal
							if (empty($agentes)) {
								echo '<!-- DEBUG EDIT: $agentes array is empty -->';
							} else {
								echo '<!-- DEBUG EDIT: Found ' . count($agentes) . ' agents -->';
							}
							foreach ($agentes as $agente): ?>
							<option value="<?php echo $agente->getId(); ?>">
								<?php echo htmlspecialchars($agente->getDescripcion()); ?>
							</option>
							<?php endforeach; ?>
						</select>
					</div>
					<div class="mb-3">
						<label for="edit_costo_usd" class="form-label">Costo USD</label>
						<input type="number" class="form-control" id="edit_costo_usd" name="costo_usd" step="0.01" min="0" placeholder="0.00">
					</div>
					<div class="mb-3">
						<label for="edit_n_mensajes" class="form-label">N° Mensajes</label>
						<input type="number" class="form-control" id="edit_n_mensajes" name="n_mensajes" min="0" value="0">
					</div>
					<div id="edit-agente-error" class="alert alert-danger" style="display: none;"></div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
					<button type="submit" class="btn btn-primary">Actualizar</button>
				</div>
			</form>
		</div>
	</div>
</div>
<?php endif; ?>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
<!-- jQuery UI for autocomplete functionality -->
<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/jquery-ui-dist/jquery-ui.min.js"></script>

<?php #region region CLIENT-SIDE VALIDATION SCRIPT ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
	const form = document.getElementById('tarea-form');
	const descripcionInput = document.getElementById('descripcion');
	const estadoInput = document.getElementById('id_tarea_estado');
	const proyectoInput = document.getElementById('id_proyecto');
	const moduloInput = document.getElementById('id_proyecto_modulo');
	const btnNuevoModulo = document.getElementById('btn-nuevo-modulo');
	const createModuloForm = document.getElementById('create-modulo-form');
	const nuevoModuloDescripcion = document.getElementById('nuevo_modulo_descripcion');
	const nuevoModuloError = document.getElementById('nuevo-modulo-error');
	const createModuloModal = new bootstrap.Modal(document.getElementById('createModuloModal'));

	// Error message elements
	const descripcionError = document.getElementById('descripcion-error');
	const estadoError = document.getElementById('id_tarea_estado-error');
	const proyectoError = document.getElementById('id_proyecto-error');

	// Handle project change to load modules and update button state
	proyectoInput.addEventListener('change', function() {
		const proyectoId = this.value;

		// Reset and disable module dropdown and button if no project selected
		if (!proyectoId) {
			moduloInput.innerHTML = '<option value="">-- Seleccione un módulo --</option>';
			moduloInput.disabled = true;
			btnNuevoModulo.disabled = true;
			return;
		}

		// Enable module dropdown and button
		moduloInput.disabled = false;
		btnNuevoModulo.disabled = false;

		// Fetch modules for the selected project using AJAX
		fetch(`get_modulos?id_proyecto=${proyectoId}`)
			.then(response => response.json())
			.then(data => {
				// Clear current options
				moduloInput.innerHTML = '<option value="">-- Seleccione un módulo --</option>';

				// Add new options
				data.forEach(modulo => {
					const option = document.createElement('option');
					option.value = modulo.id;
					option.textContent = modulo.descripcion;
					moduloInput.appendChild(option);
				});
			})
			.catch(error => {
				console.error('Error fetching modules:', error);
				moduloInput.innerHTML = '<option value="">Error al cargar módulos</option>';
			});
	});

	// Handle create module form submission
	createModuloForm.addEventListener('submit', function(event) {
		event.preventDefault();

		// Get form data
		const descripcion = nuevoModuloDescripcion.value.trim();
		const id_proyecto = proyectoInput.value;

		// Validate form data
		if (!descripcion) {
			nuevoModuloError.textContent = 'La descripción es requerida.';
			nuevoModuloError.style.display = 'block';
			return;
		}

		// Hide error message
		nuevoModuloError.style.display = 'none';

		// Create form data for AJAX request
		const formData = new FormData();
		formData.append('action', 'crear');
		formData.append('id_proyecto', id_proyecto);
		formData.append('descripcion', descripcion);

		// Send AJAX request
		fetch('lproyectos_modulos_ajax', {
			method: 'POST',
			body: formData
		})
		.then(response => {
			if (!response.ok) {
				throw new Error(`HTTP error! Status: ${response.status}`);
			}
			return response.json();
		})
		.then(data => {
			console.log('Response data:', data); // Debug log
			if (data.success) {
				// Add new module to dropdown and select it
				const option = document.createElement('option');
				option.value = data.id;
				option.textContent = descripcion;
				moduloInput.appendChild(option);
				moduloInput.value = data.id;

				// Reset form and close modal
				nuevoModuloDescripcion.value = '';
				createModuloModal.hide();
			} else {
				// Show error message
				nuevoModuloError.textContent = data.message || 'Error al crear el módulo.';
				nuevoModuloError.style.display = 'block';
			}
		})
		.catch(error => {
			console.error('Error creating module:', error);
			nuevoModuloError.textContent = 'Error al crear el módulo. Inténtelo de nuevo.';
			nuevoModuloError.style.display = 'block';
		});
	});

	// Form submission validation
	form.addEventListener('submit', function(event) {
		let isValid = true;

		// Reset validation state
		descripcionInput.classList.remove('is-invalid');
		estadoInput.classList.remove('is-invalid');
		proyectoInput.classList.remove('is-invalid');

		// Validate descripcion
		if (!descripcionInput.value.trim()) {
			descripcionInput.classList.add('is-invalid');
			descripcionError.textContent = 'La descripción es requerida.';
			isValid = false;
		}

		// Validate estado
		if (!estadoInput.value) {
			estadoInput.classList.add('is-invalid');
			estadoError.textContent = 'El estado es requerido.';
			isValid = false;
		}

		// Validate proyecto (required)
		if (!proyectoInput.value) {
			proyectoInput.classList.add('is-invalid');
			proyectoError.textContent = 'El proyecto es requerido.';
			isValid = false;
		}

		if (!isValid) {
			event.preventDefault();
		}
	});

	// Real-time validation
	descripcionInput.addEventListener('input', function() {
		if (this.value.trim()) {
			this.classList.remove('is-invalid');
		}
	});

	estadoInput.addEventListener('change', function() {
		if (this.value) {
			this.classList.remove('is-invalid');
		}
	});

	proyectoInput.addEventListener('change', function() {
		if (this.value) {
			this.classList.remove('is-invalid');
		}
	});

	// Tarea Padre Autocomplete functionality
	const tareaPadreAutocomplete = document.getElementById('tarea_padre_autocomplete');
	const tareaPadreHidden = document.getElementById('id_tarea_padre');
	const tareaPadreLoading = document.getElementById('tarea-padre-loading');
	const tareaPadreError = document.getElementById('tarea-padre-error');

	if (tareaPadreAutocomplete) {
		let searchTimeout;
		let currentAbortController;

		// Initialize jQuery UI autocomplete
		$(tareaPadreAutocomplete).autocomplete({
			minLength: 2,
			delay: 300,
			source: function(request, response) {
				// Clear previous timeout
				if (searchTimeout) {
					clearTimeout(searchTimeout);
				}

				// Abort previous request using AbortController
				if (currentAbortController) {
					currentAbortController.abort();
				}

				// Show loading indicator
				tareaPadreLoading.style.display = 'block';
				tareaPadreError.style.display = 'none';

				// Set timeout for search
				searchTimeout = setTimeout(function() {
					// Create new AbortController for this request
					currentAbortController = new AbortController();

					const formData = new FormData();
					formData.append('query', request.term);
					<?php if ($is_edit_mode && $tarea): ?>
					formData.append('exclude_id', <?php echo $tarea->getId(); ?>);
					<?php endif; ?>

					fetch('search_tareas_ajax', {
						method: 'POST',
						body: formData,
						headers: {
							'X-Requested-With': 'XMLHttpRequest'
						},
						signal: currentAbortController.signal
					})
					.then(responseObj => {
						if (!responseObj.ok) {
							throw new Error(`HTTP error! Status: ${responseObj.status}`);
						}
						return responseObj.json();
					})
					.then(data => {
						tareaPadreLoading.style.display = 'none';

						if (data.success) {
							// Transform results for jQuery UI autocomplete
							const autocompleteResults = data.results.map(item => ({
								label: item.display_text,
								value: item.display_text,
								id: item.id,
								descripcion: item.descripcion,
								nombre_proyecto: item.nombre_proyecto,
								nombre_tarea_estado: item.nombre_tarea_estado,
								bg_color: item.bg_color
							}));
							response(autocompleteResults);
						} else {
							tareaPadreError.textContent = data.message || 'Error al buscar tareas.';
							tareaPadreError.style.display = 'block';
							response([]);
						}
					})
					.catch(error => {
						tareaPadreLoading.style.display = 'none';

						// Don't show error for aborted requests
						if (error.name === 'AbortError') {
							console.log('Search request was cancelled');
							return;
						}

						console.error('Error searching tasks:', error);
						tareaPadreError.textContent = 'Error de comunicación al buscar tareas.';
						tareaPadreError.style.display = 'block';
						response([]);
					});
				}, 300);
			},
			select: function(event, ui) {
				// Set the hidden field value
				tareaPadreHidden.value = ui.item.id;
				tareaPadreError.style.display = 'none';
				return true;
			},
			focus: function(event, ui) {
				// Prevent the input from being updated on focus
				return false;
			}
		}).autocomplete('instance')._renderItem = function(ul, item) {
			// Custom rendering for autocomplete items
			const listItem = $('<li>');
			const itemContent = $('<div class="autocomplete-item">');

			// Task description
			const taskDesc = $('<div class="task-description fw-bold">').text(item.descripcion);
			itemContent.append(taskDesc);

			// Project name (if available)
			if (item.nombre_proyecto) {
				const projectName = $('<div class="project-name text-muted small">').text('Proyecto: ' + item.nombre_proyecto);
				itemContent.append(projectName);
			}

			// Task status (if available)
			if (item.nombre_tarea_estado) {
				const statusBadge = $('<span class="badge badge-sm ms-2">').text(item.nombre_tarea_estado);
				if (item.bg_color) {
					// Use the bg_color from database as CSS class (same as ltareas.view.php)
					statusBadge.addClass(item.bg_color);
				} else {
					// Fallback to primary if no bg_color is set
					statusBadge.addClass('bg-primary');
				}
				taskDesc.append(statusBadge);
			}

			listItem.append(itemContent);
			return listItem.appendTo(ul);
		};

		// Clear hidden field when input is manually cleared
		tareaPadreAutocomplete.addEventListener('input', function() {
			if (this.value.trim() === '') {
				tareaPadreHidden.value = '';
				tareaPadreError.style.display = 'none';
			}
		});

		// Handle blur event to validate selection
		tareaPadreAutocomplete.addEventListener('blur', function() {
			// If there's text but no ID selected, clear the field
			if (this.value.trim() !== '' && tareaPadreHidden.value === '') {
				setTimeout(() => {
					// Only clear if autocomplete menu is not open
					if (!$(this).autocomplete('widget').is(':visible')) {
						this.value = '';
						tareaPadreHidden.value = '';
					}
				}, 200);
			}
		});
	}

	// Module Autocomplete functionality
	const moduloAutocomplete = document.getElementById('modulo_autocomplete');
	const moduloHidden = document.getElementById('id_proyecto_modulo');
	const moduloLoading = document.getElementById('modulo-loading');
	const moduloError = document.getElementById('modulo-error');
	const proyectoSelect = document.getElementById('id_proyecto');

	if (moduloAutocomplete && proyectoSelect) {
		let searchTimeout;
		let currentAbortController;

		// Initialize jQuery UI autocomplete
		$(moduloAutocomplete).autocomplete({
			minLength: 2,
			delay: 300,
			disabled: !proyectoSelect.value, // Disable if no project selected
			source: function(request, response) {
				// Check if project is selected
				if (!proyectoSelect.value) {
					response([]);
					return;
				}

				// Clear previous timeout
				if (searchTimeout) {
					clearTimeout(searchTimeout);
				}

				// Abort previous request
				if (currentAbortController) {
					currentAbortController.abort();
				}

				// Create new AbortController for this request
				currentAbortController = new AbortController();

				// Show loading indicator
				moduloLoading.style.display = 'block';
				moduloError.style.display = 'none';

				// Set timeout for the search
				searchTimeout = setTimeout(() => {
					fetch(`search_modulos_ajax?query=${encodeURIComponent(request.term)}&id_proyecto=${proyectoSelect.value}&limit=10`, {
						signal: currentAbortController.signal
					})
					.then(response => {
						if (!response.ok) {
							throw new Error(`HTTP error! status: ${response.status}`);
						}
						return response.json();
					})
					.then(data => {
						moduloLoading.style.display = 'none';

						if (data.success) {
							// Transform results for jQuery UI autocomplete
							const autocompleteResults = data.results.map(item => ({
								label: item.display_text,
								value: item.display_text,
								id: item.id,
								descripcion: item.descripcion
							}));
							response(autocompleteResults);
						} else {
							moduloError.textContent = data.message || 'Error al buscar módulos.';
							moduloError.style.display = 'block';
							response([]);
						}
					})
					.catch(error => {
						moduloLoading.style.display = 'none';
						if (error.name !== 'AbortError') {
							console.error('Error searching modules:', error);
							moduloError.textContent = 'Error al buscar módulos. Inténtelo de nuevo.';
							moduloError.style.display = 'block';
						}
						response([]);
					});
				}, 300);
			},
			select: function(event, ui) {
				// Set the hidden field value
				moduloHidden.value = ui.item.id;
				moduloError.style.display = 'none';
				return true;
			}
		});

		// Clear hidden field when input is manually cleared
		moduloAutocomplete.addEventListener('input', function() {
			if (this.value.trim() === '') {
				moduloHidden.value = '';
				moduloError.style.display = 'none';
			}
		});

		// Handle blur event to validate selection
		moduloAutocomplete.addEventListener('blur', function() {
			// If there's text but no ID selected, clear the field
			if (this.value.trim() !== '' && moduloHidden.value === '') {
				setTimeout(() => {
					// Only clear if autocomplete menu is not open
					if (!$(this).autocomplete('widget').is(':visible')) {
						this.value = '';
						moduloHidden.value = '';
					}
				}, 200);
			}
		});

		// Handle project change - enable/disable module autocomplete
		proyectoSelect.addEventListener('change', function() {
			const btnNuevoModulo = document.getElementById('btn-nuevo-modulo');

			if (this.value) {
				// Enable module autocomplete
				moduloAutocomplete.disabled = false;
				$(moduloAutocomplete).autocomplete('enable');
				moduloAutocomplete.placeholder = 'Escriba para buscar un módulo...';

				// Enable "Nuevo Módulo" button
				if (btnNuevoModulo) {
					btnNuevoModulo.disabled = false;
				}
			} else {
				// Disable module autocomplete and clear values
				moduloAutocomplete.disabled = true;
				$(moduloAutocomplete).autocomplete('disable');
				moduloAutocomplete.value = '';
				moduloHidden.value = '';
				moduloAutocomplete.placeholder = 'Seleccione un proyecto primero...';

				// Disable "Nuevo Módulo" button
				if (btnNuevoModulo) {
					btnNuevoModulo.disabled = true;
				}
			}
			moduloError.style.display = 'none';
		});
	}

	<?php if ($is_edit_mode && $tarea): ?>
	// TareaAgente management functionality
	const addAgenteForm = document.getElementById('add-agente-form');
	const editAgenteForm = document.getElementById('edit-agente-form');
	const agentesTableBody = document.getElementById('agentes-table-body');
	const addAgenteFeedback = document.getElementById('add-agente-feedback');
	const editAgenteError = document.getElementById('edit-agente-error');
	const editAgenteModal = new bootstrap.Modal(document.getElementById('editAgenteModal'));

	// Add agent form submission
	if (addAgenteForm) {
		addAgenteForm.addEventListener('submit', function(event) {
			event.preventDefault(); // Always prevent default submission for AJAX

			let isValid = true;
			const agenteInput = document.getElementById('id_agente');
			const costoInput = document.getElementById('costo_usd');
			const mensajesInput = document.getElementById('n_mensajes');

			// Reset previous validation states
			[agenteInput, costoInput, mensajesInput].forEach(input => {
				if (input) input.classList.remove('is-invalid');
			});

			// Validate Agente selection
			if (agenteInput && agenteInput.value.trim() === '') {
				isValid = false;
				agenteInput.classList.add('is-invalid');
			}

			// Validate Costo (optional but if provided, must be >= 0)
			if (costoInput && costoInput.value !== '' && parseFloat(costoInput.value) < 0) {
				isValid = false;
				costoInput.classList.add('is-invalid');
			}

			// Validate Mensajes (optional but if provided, must be >= 0)
			if (mensajesInput && mensajesInput.value !== '' && parseInt(mensajesInput.value) < 0) {
				isValid = false;
				mensajesInput.classList.add('is-invalid');
			}

			// If client-side validation fails, show feedback and stop
			if (!isValid) {
				event.stopPropagation();
				addAgenteFeedback.innerHTML = `<div class="alert alert-warning">Por favor corrija los campos marcados.</div>`;
				const firstInvalid = addAgenteForm.querySelector('.is-invalid');
				if (firstInvalid) firstInvalid.focus();
				return;
			}

			// Client-side validation passed, proceed with AJAX
			addAgenteFeedback.innerHTML = `<div class="alert alert-info">Asignando agente... <span class="spinner-border spinner-border-sm"></span></div>`;

			const formData = new FormData();
			formData.append('action', 'crear');
			formData.append('id_tarea', <?php echo $tarea->getId(); ?>);
			formData.append('id_agente', agenteInput.value);
			formData.append('costo_usd', costoInput.value || 0);
			formData.append('n_mensajes', mensajesInput.value || 0);

			fetch('ltarea_agentes_ajax', {
				method: 'POST',
				body: formData,
				headers: {
					'X-Requested-With': 'XMLHttpRequest'
				}
			})
			.then(response => {
				if (!response.ok) {
					return response.json().then(errData => {
						throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
					});
				}
				return response.json();
			})
			.then(data => {
				if (data.success) {
					// Success
					addAgenteFeedback.innerHTML = `<div class="alert alert-success alert-dismissible fade show" role="alert">
						${data.message}
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>`;
					addAgenteToTable(data.tarea_agente);
					addAgenteForm.reset();
					// Remove any lingering validation classes
					[agenteInput, costoInput, mensajesInput].forEach(input => {
						if (input) input.classList.remove('is-invalid');
					});
				} else {
					// Server-side validation or other error
					addAgenteFeedback.innerHTML = `<div class="alert alert-danger">${data.message || 'Ocurrió un error inesperado.'}</div>`;
				}
			})
			.catch(error => {
				// Network error or JSON parsing error
				console.error('Error adding agent via AJAX:', error);
				addAgenteFeedback.innerHTML = `<div class="alert alert-danger">Error de comunicación al asignar el agente: ${error.message}</div>`;
			});
		});
	}

	// Edit agent form submission
	if (editAgenteForm) {
		editAgenteForm.addEventListener('submit', function(event) {
			event.preventDefault();

			const formData = new FormData();
			formData.append('action', 'modificar');
			formData.append('id', document.getElementById('edit-tarea-agente-id').value);
			formData.append('id_tarea', <?php echo $tarea->getId(); ?>);
			formData.append('id_agente', document.getElementById('edit_id_agente').value);
			formData.append('costo_usd', document.getElementById('edit_costo_usd').value || 0);
			formData.append('n_mensajes', document.getElementById('edit_n_mensajes').value || 0);

			// Hide previous errors
			editAgenteError.style.display = 'none';

			fetch('ltarea_agentes_ajax', {
				method: 'POST',
				body: formData
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					// Update row in table
					updateAgenteInTable(data.tarea_agente);
					// Close modal
					editAgenteModal.hide();
				} else {
					editAgenteError.textContent = data.message || 'Error al actualizar agente.';
					editAgenteError.style.display = 'block';
				}
			})
			.catch(error => {
				console.error('Error:', error);
				editAgenteError.textContent = 'Error al actualizar agente. Inténtelo de nuevo.';
				editAgenteError.style.display = 'block';
			});
		});
	}

	// Handle edit and delete buttons
	if (agentesTableBody) {
		agentesTableBody.addEventListener('click', function(event) {
			const editButton = event.target.closest('.btn-edit-agente');
			const deleteButton = event.target.closest('.btn-delete-agente');

			if (editButton) {
				event.preventDefault();
				const row = editButton.closest('tr');
				const id = editButton.dataset.agenteId;

				// Get current values from the row
				const agenteSelect = row.querySelector('.agente-nombre').textContent.trim();
				const costoText = row.querySelector('.costo-usd').textContent.replace('$', '').replace(',', '');
				const mensajes = row.querySelector('.n-mensajes').textContent.trim();

				// Find agent ID by name
				const agenteOptions = document.getElementById('edit_id_agente').options;
				let agenteId = '';
				for (let i = 0; i < agenteOptions.length; i++) {
					if (agenteOptions[i].textContent.trim() === agenteSelect) {
						agenteId = agenteOptions[i].value;
						break;
					}
				}

				// Populate edit form
				document.getElementById('edit-tarea-agente-id').value = id;
				document.getElementById('edit_id_agente').value = agenteId;
				document.getElementById('edit_costo_usd').value = parseFloat(costoText) || 0;
				document.getElementById('edit_n_mensajes').value = parseInt(mensajes) || 0;

				// Show modal
				editAgenteModal.show();
			}

			if (deleteButton) {
				event.preventDefault();
				const row = deleteButton.closest('tr');
				const id = deleteButton.dataset.agenteId;
				const agenteNombre = deleteButton.dataset.agenteNombre || 'este agente';

				// Use SweetAlert for confirmation (matching reference pattern)
				swal({
					title: "Confirmar Eliminación",
					text: `¿Seguro que quieres eliminar la asignación del agente '${agenteNombre}'? Esta acción no se puede deshacer.`,
					icon: "warning",
					buttons: {
						cancel: { text: "Cancelar", value: null, visible: true, className: "", closeModal: true },
						confirm: { text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true }
					},
					dangerMode: true,
				})
				.then((willDelete) => {
					if (willDelete) {
						const formData = new FormData();
						formData.append('action', 'eliminar');
						formData.append('id', id);

						fetch('ltarea_agentes_ajax', {
							method: 'POST',
							body: formData,
							headers: { 'X-Requested-With': 'XMLHttpRequest' }
						})
						.then(response => response.json())
						.then(data => {
							if (data.success) {
								// Remove row from table
								row.remove();
								showSweetAlertSuccess('Eliminado', data.message || 'Agente eliminado correctamente.');
								// Add back "No hay agentes" message if table is now empty
								if (agentesTableBody.rows.length === 0) {
									agentesTableBody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No hay agentes asignados a esta tarea.</td></tr>';
								}
							} else {
								showSweetAlertError('Error', data.message || 'No se pudo eliminar el agente.');
							}
						})
						.catch(error => {
							console.error('Error deleting agent:', error);
							showSweetAlertError('Error', 'Error de comunicación al eliminar el agente: ' + error.message);
						});
					}
				});
			}
		});
	}

	// Helper function to add agent to table
	function addAgenteToTable(tareaAgente) {
		const tbody = document.getElementById('agentes-table-body');
		if (!tbody) return;

		// Remove the "No hay agentes" message if it exists
		const noAgentsRow = tbody.querySelector('td[colspan="4"]');
		if (noAgentsRow) {
			noAgentsRow.closest('tr').remove();
		}

		const newRow = tbody.insertRow(); // Appends to the end by default
		newRow.dataset.id = tareaAgente.id;
		newRow.innerHTML = `
			<td class="text-center">
				<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-agente"
				        title="Editar"
				        data-agente-id="${tareaAgente.id}">
					<i class="fa fa-edit"></i>
				</button>
				<button type="button" class="btn btn-xs btn-danger btn-delete-agente"
				        title="Eliminar"
				        data-agente-id="${tareaAgente.id}"
				        data-agente-nombre="${tareaAgente.agente_nombre}">
					<i class="fa fa-trash-alt"></i>
				</button>
			</td>
			<td class="agente-nombre">${tareaAgente.agente_nombre}</td>
			<td class="text-end costo-usd">$${parseFloat(tareaAgente.costo_usd || 0).toFixed(2)}</td>
			<td class="text-center n-mensajes">${tareaAgente.n_mensajes || 0}</td>
		`;
	}

	// Helper function to update agent in table
	function updateAgenteInTable(tareaAgente) {
		const row = document.querySelector(`tr[data-id="${tareaAgente.id}"]`);
		if (row) {
			row.querySelector('.agente-nombre').textContent = tareaAgente.agente_nombre;
			row.querySelector('.costo-usd').textContent = `$${parseFloat(tareaAgente.costo_usd || 0).toFixed(2)}`;
			row.querySelector('.n-mensajes').textContent = tareaAgente.n_mensajes || 0;
			// Update data attributes for delete button
			const deleteBtn = row.querySelector('.btn-delete-agente');
			if (deleteBtn) {
				deleteBtn.setAttribute('data-agente-nombre', tareaAgente.agente_nombre);
			}
		}
	}
	<?php endif; ?>
});

// Function to redirect to tasks with project filter
function redirectToTasksWithProject(projectId) {
	// Create a form to submit the project ID via session
	const form = document.createElement('form');
	form.method = 'GET';
	form.action = 'lproyectos';

	const actionInput = document.createElement('input');
	actionInput.type = 'hidden';
	actionInput.name = 'action';
	actionInput.value = 'ver_tareas';

	const projectInput = document.createElement('input');
	projectInput.type = 'hidden';
	projectInput.name = 'proyecto_id';
	projectInput.value = projectId;

	form.appendChild(actionInput);
	form.appendChild(projectInput);
	document.body.appendChild(form);
	form.submit();
}
</script>
<?php #endregion CLIENT-SIDE VALIDATION SCRIPT ?>

<?php #endregion JS ?>

</body>
</html>
