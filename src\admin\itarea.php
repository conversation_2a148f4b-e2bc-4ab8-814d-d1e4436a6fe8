<?php

declare(strict_types=1);

use App\classes\Tarea;
use App\classes\Proyecto;
use App\classes\ProyectoModulo;
use App\classes\Agente;
use App\classes\TareaAgente;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion; // Assuming $conexion is globally available or included

// Include necessary files
require_once dirname(__DIR__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en itarea.php");
	$_SESSION['flash_message_error'] = 'Error crítico: No se pudo conectar a la base de datos.';
	header('Location: ltareas');
	exit;
}

#region region INIT VARIABLES
// Variables to hold form input (useful if re-displaying form after error)
$descripcion        = '';
$id_tarea_estado    = Tarea::ESTADO_PENDIENTE; // Default to pending
$id_proyecto        = null;
$id_proyecto_modulo = null;
$id_tarea_padre     = null;
$tarea              = null;
$is_edit_mode       = false;
$error_text         = '';
$error_display      = 'none';

// Get projects for dropdown
try {
	$proyectos = Proyecto::get_list($conexion);
} catch (Exception $e) {
	$proyectos = [];
	error_log("Error loading projects: " . $e->getMessage());
}

// Initialize modules array (will be populated based on selected project)
$modulos = [];

// Get agentes for dropdown
try {
	$agentes = Agente::get_list($conexion, true); // Only active agents
} catch (Exception $e) {
	$agentes = [];
	error_log("Error loading agents: " . $e->getMessage());
}

// Initialize tarea_agentes array
$tarea_agentes = [];

// Initialize tareas_padre array (will be populated later based on edit mode)
$tareas_padre = [];
#endregion INIT VARIABLES

#region region Check if Edit Mode
$tarea_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
if ($tarea_id) {
	try {
		$tarea = Tarea::get($tarea_id, $conexion);
		if ($tarea) {
			$is_edit_mode       = true;
			$descripcion        = $tarea->getDescripcion() ?? '';
			$id_tarea_estado    = $tarea->getIdTareaEstado() ?? Tarea::ESTADO_PENDIENTE;
			$id_proyecto        = $tarea->getIdProyecto();
			$id_proyecto_modulo = $tarea->getIdProyectoModulo();
			$id_tarea_padre     = $tarea->getIdTareaPadre();

			// If project is selected, load its modules
			if ($id_proyecto) {
				try {
					$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);
				} catch (Exception $e) {
					$modulos = [];
					error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
				}
			}

			// Load TareaAgente associations for this task with agent descriptions (optimized)
			try {
				$tarea_agentes = TareaAgente::getByTareaIdWithAgentDescriptions($tarea_id, $conexion);
			} catch (Exception $e) {
				$tarea_agentes = [];
				error_log("Error loading TareaAgente with agent descriptions for task ID $tarea_id: " . $e->getMessage());
			}

			// Load potential parent tasks (exclude current task to avoid circular references)
			try {
				$tareas_padre = Tarea::getPotentialParentTasks($tarea_id, $conexion);
			} catch (Exception $e) {
				$tareas_padre = [];
				error_log("Error loading potential parent tasks for task ID $tarea_id: " . $e->getMessage());
			}
		} else {
			$_SESSION['flash_message_error'] = "Error: No se encontró la tarea con ID $tarea_id.";
			header('Location: ltareas');
			exit;
		}
	} catch (Exception $e) {
		$_SESSION['flash_message_error'] = "Error al cargar la tarea: " . $e->getMessage();
		header('Location: ltareas');
		exit;
	}
} else {
	// Check for proyecto_id parameter for pre-populating project (from project navigation)
	$proyecto_id = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
	if ($proyecto_id) {
		try {
			// Validate that the project exists
			$proyecto = Proyecto::get($proyecto_id, $conexion);
			if ($proyecto) {
				$id_proyecto = $proyecto_id;

				// Load modules for the selected project
				try {
					$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);
				} catch (Exception $e) {
					$modulos = [];
					error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
				}
			} else {
				$_SESSION['flash_message_error'] = "Error: No se encontró el proyecto con ID $proyecto_id.";
				header('Location: ltareas');
				exit;
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al cargar el proyecto: " . $e->getMessage();
			header('Location: ltareas');
			exit;
		}
	}

	// Check for parent_id parameter for creating child tasks
	$parent_id = filter_input(INPUT_GET, 'parent_id', FILTER_VALIDATE_INT);
	$parent_task = null; // Store parent task for display purposes
	if ($parent_id) {
		try {
			// Validate that the parent task exists
			$parent_task = Tarea::get($parent_id, $conexion);
			if ($parent_task) {
				$id_tarea_padre = $parent_id;
				// Pre-populate project and module from parent task for consistency (only if not already set by proyecto_id)
				if (!$id_proyecto) {
					$id_proyecto = $parent_task->getIdProyecto();
					$id_proyecto_modulo = $parent_task->getIdProyectoModulo();

					// If project is selected, load its modules
					if ($id_proyecto) {
						try {
							$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);
						} catch (Exception $e) {
							$modulos = [];
							error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
						}
					}
				}
			} else {
				$_SESSION['flash_message_error'] = "Error: No se encontró la tarea padre con ID $parent_id.";
				header('Location: ltareas');
				exit;
			}
		} catch (Exception $e) {
			$_SESSION['flash_message_error'] = "Error al cargar la tarea padre: " . $e->getMessage();
			header('Location: ltareas');
			exit;
		}
	}

	// For new task creation, load all potential parent tasks
	try {
		$tareas_padre = Tarea::getPotentialParentTasks(null, $conexion);
	} catch (Exception $e) {
		$tareas_padre = [];
		error_log("Error loading potential parent tasks for new task: " . $e->getMessage());
	}
}
#endregion Check if Edit Mode

#region region POST Request Handling
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	try {
		// 1. Get data from $_POST
		$descripcion        = trim($_POST['descripcion'] ?? '');
		$id_tarea_estado    = filter_input(INPUT_POST, 'id_tarea_estado', FILTER_VALIDATE_INT) ?? Tarea::ESTADO_PENDIENTE;

		// Handle project field - convert false to null when empty
		$id_proyecto        = filter_input(INPUT_POST, 'id_proyecto', FILTER_VALIDATE_INT);
		if ($id_proyecto === false) {
			$id_proyecto = null;
		}

		// Handle module field - convert false to null when empty
		$id_proyecto_modulo = filter_input(INPUT_POST, 'id_proyecto_modulo', FILTER_VALIDATE_INT);
		if ($id_proyecto_modulo === false) {
			$id_proyecto_modulo = null;
		}

		// Handle parent task field - convert false to null when empty
		$id_tarea_padre = filter_input(INPUT_POST, 'id_tarea_padre', FILTER_VALIDATE_INT);
		if ($id_tarea_padre === false) {
			$id_tarea_padre = null;
		}

		// If project is selected, load its modules for the form
		if ($id_proyecto) {
			try {
				$modulos = ProyectoModulo::getByProyecto($id_proyecto, $conexion);

			} catch (Exception $e) {
				$modulos = [];
				error_log("Error loading modules for project ID $id_proyecto: " . $e->getMessage());
			}
		}

		// Reload potential parent tasks for form redisplay in case of errors
		try {
			$tareas_padre = Tarea::getPotentialParentTasks($is_edit_mode && $tarea ? $tarea->getId() : null, $conexion);
		} catch (Exception $e) {
			$tareas_padre = [];
			error_log("Error reloading potential parent tasks: " . $e->getMessage());
		}

		// 2. Validate data
		if (empty($descripcion)) {
			throw new Exception("La descripción de la tarea es requerida.");
		}

		if (empty($id_proyecto)) {
			throw new Exception("El proyecto es requerido.");
		}

		// Validate parent task to prevent circular references
		if ($id_tarea_padre !== null && $is_edit_mode && $tarea) {
			if (!Tarea::isValidParentChild($id_tarea_padre, $tarea->getId(), $conexion)) {
				throw new Exception("La tarea seleccionada como padre no es válida. No se pueden crear referencias circulares.");
			}
		}

		// 3. Create or update Tarea
		if ($is_edit_mode && $tarea) {
			// Update existing tarea
			$tarea->setDescripcion($descripcion);
			$tarea->setIdTareaEstado($id_tarea_estado);
			$tarea->setIdProyecto($id_proyecto);
			$tarea->setIdProyectoModulo($id_proyecto_modulo);
			$tarea->setIdTareaPadre($id_tarea_padre);

			$success = $tarea->modificar($conexion);

			if ($success) {
				$_SESSION['flash_message_success'] = "Tarea actualizada exitosamente.";

				// If we came from a project context, redirect back with project filter
				$proyecto_id_param = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
				if ($proyecto_id_param) {
					$_SESSION['filtro_proyecto_id'] = $proyecto_id_param;
				}

				header('Location: ltareas');
				exit;
			} else {
				throw new Exception("Hubo un error al actualizar la tarea. Intente nuevamente.");
			}
		} else {
			// Create new tarea
			$tarea = new Tarea();
			$tarea->setDescripcion($descripcion);
			$tarea->setIdTareaEstado($id_tarea_estado);
			$tarea->setIdProyecto($id_proyecto);
			$tarea->setIdProyectoModulo($id_proyecto_modulo);
			$tarea->setIdTareaPadre($id_tarea_padre);

			$newId = $tarea->crear($conexion);

			if ($newId !== false && $newId > 0) {
				$_SESSION['flash_message_success'] = "Tarea creada exitosamente.";

				// If we came from a project context, redirect back with project filter
				$proyecto_id_param = filter_input(INPUT_GET, 'proyecto_id', FILTER_VALIDATE_INT);
				if ($proyecto_id_param) {
					$_SESSION['filtro_proyecto_id'] = $proyecto_id_param;
				}

				header('Location: ltareas');
				exit;
			} else {
				throw new Exception("Hubo un error al guardar la tarea. Intente nuevamente.");
			}
		}

	} catch (PDOException $e) {
		// Handle potential database errors
		error_log("Database error: " . $e->getMessage());
		$error_text    = 'Error de base de datos al ' . ($is_edit_mode ? 'actualizar' : 'crear') . ' la tarea. Por favor, contacte al administrador.';
		$error_display = 'show';
	} catch (Exception $e) {
		// Handle other potential errors
		error_log("General error: " . $e->getMessage());
		$error_text    = 'Ocurrió un error inesperado: ' . $e->getMessage();
		$error_display = 'show';
	}
}
#endregion POST Request Handling

// Load the view file. Variables defined above will be available in the view.
require_once __ROOT__ . '/views/admin/itarea.view.php';
