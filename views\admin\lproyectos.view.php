<?php
#region region DOCS

/** @var Proyecto[] $proyectos */

use App\classes\Proyecto;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Proyectos</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Proyectos</h4>
				<p class="mb-0 text-muted">Administra los proyectos del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="iproyecto" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nuevo</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL PROYECTOS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Proyectos Activos
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto">
				<?php #region region TABLE PROYECTOS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th>Acciones</th>
						<th>#</th>
						<th>Descripción</th>
						<th class="text-center">Fecha de Creación</th>
						<th class="text-center">Fecha de Inicio</th>
						<th class="text-center">Estado</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="proyecto-table-body">
					<?php foreach ($proyectos as $proyecto): ?>
						<tr data-proyecto-id="<?php echo $proyecto->getId(); ?>">
							<td>
								<?php // Edit Button - Triggers Modal ?>
									<a href="eproyecto?id=<?php echo $proyecto->getId(); ?>" class="btn btn-xs btn-primary me-1 btn-edit-proyecto"
									title="Editar Proyecto">
										<i class="fa fa-edit"></i>
									</a>
								<?php // Ver Tareas Button ?>
								<a href="lproyectos?action=ver_tareas&proyecto_id=<?php echo $proyecto->getId(); ?>" class="btn btn-xs btn-info me-1"
								   title="Ver Tareas del Proyecto">
									<i class="fa fa-tasks"></i>
								</a>
								<?php // Deactivate Button - Only show if active ?>
								<?php if ($proyecto->isActivo()): ?>
									<button type="button" class="btn btn-xs btn-danger btn-desactivar-proyecto"
									        title="Desactivar"
									        data-proyectoid="<?php echo $proyecto->getId(); ?>"
									        data-descripcion="<?php echo htmlspecialchars($proyecto->getDescripcion() ?? ''); ?>">
										<i class="fa fa-trash-alt"></i>
									</button>
								<?php endif; ?>
							</td>
							<td><?php echo htmlspecialchars($proyecto->getId()); ?></td>
							<td class="proyecto-descripcion-cell"><?php echo htmlspecialchars($proyecto->getDescripcion()); ?></td>
							<td class="text-center"><?php echo htmlspecialchars($proyecto->getFechaCreacion()); ?></td>
							<td class="text-center"><?php echo htmlspecialchars($proyecto->getFechaInicio() ?? 'N/A'); ?></td>
							<td class="text-center">
								<?php if ($proyecto->isActivo()): ?>
									<span class="badge bg-success">Activo</span>
								<?php else: ?>
									<span class="badge bg-danger">Inactivo</span>
								<?php endif; ?>
							</td>
						</tr>
					<?php endforeach; ?>
					<?php if (empty($proyectos)): ?>
						<tr>
							<td colspan="6" class="text-center">No hay proyectos para mostrar.</td>
						</tr>
					<?php endif; ?>

					</tbody>
				</table>
				<?php #endregion TABLE PROYECTOS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL PROYECTOS ?>
		<?php #region region Edit Proyecto Modal ?>
		<div class="modal fade" id="editProyectoModal" tabindex="-1" aria-labelledby="editProyectoModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<form id="edit-proyecto-form">
						<div class="modal-header">
							<h5 class="modal-title" id="editProyectoModalLabel">Editar Descripción del Proyecto</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<input type="hidden" name="proyectoId" id="edit-proyecto-id">
							<input type="hidden" name="action" value="modificar">

							<div class="mb-3">
								<label for="edit-proyecto-descripcion" class="form-label">Descripción:</label>
								<input type="text" class="form-control" id="edit-proyecto-descripcion" name="descripcion" required>
							</div>
							<div id="edit-proyecto-error" class="alert alert-danger mt-2" style="display: none;"></div>
						</div>
						<div class="modal-footer">
							<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
							<button type="submit" class="btn btn-primary">Guardar Cambios</button>
						</div>
					</form>
				</div>
			</div>
		</div>
		<?php #endregion Edit Proyecto Modal ?>

	</div>
	<!-- END #content -->

	<?php #region region Hidden Form for Deactivation ?>
	<form id="deactivate-proyecto-form" method="POST" action="lproyectos" style="display: none;">
		<input type="hidden" name="action" value="desactivar">
		<input type="hidden" name="proyectoId" id="deactivate-proyecto-id">
	</form>
	<?php #endregion Hidden Form ?>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<!-- Assuming SweetAlert is loaded in core_js or globally -->
<!-- Assuming SweetAlert helper functions (showSweetAlertSuccess/Error) are available -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Use event delegation on the table body
        const tableBody            = document.getElementById('proyecto-table-body');
        const editProyectoModalElement = document.getElementById('editProyectoModal');
        const editProyectoModal        = new bootstrap.Modal(editProyectoModalElement); // Initialize Bootstrap Modal
        const editProyectoForm         = document.getElementById('edit-proyecto-form');
        const editProyectoIdInput      = document.getElementById('edit-proyecto-id');
        const editProyectoDescripcionInput  = document.getElementById('edit-proyecto-descripcion');
        const editProyectoErrorDiv     = document.getElementById('edit-proyecto-error');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-desactivar-proyecto');
                const editButton       = event.target.closest('.btn-edit-proyecto');

                // --- Handle Deactivate Click ---
				<?php #region region JS AJAX -- Deactivate proyecto ?>
                if (deactivateButton) {
                    event.preventDefault();
                    const proyectoId   = deactivateButton.dataset.proyectoid;
                    const descripcion = deactivateButton.dataset.descripcion || 'este proyecto';

                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar el proyecto '${descripcion}'?`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDelete) => {
                            if (willDelete) {
                                document.getElementById('deactivate-proyecto-id').value = proyectoId;
                                document.getElementById('deactivate-proyecto-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Deactivate proyecto ?>

                // --- Handle Edit Click ---
<?php #region region JS AJAX - Handle Edit click ?>
                if (editButton) {
                    // Remove event.preventDefault() to allow the default action of the <a> tag
                    const proyectoId        = editButton.dataset.proyectoid;
                    const currentDescripcion = editButton.dataset.descripcion;

                    // Populate the modal form
                    editProyectoIdInput.value          = proyectoId;
                    editProyectoDescripcionInput.value      = currentDescripcion;
                    editProyectoErrorDiv.style.display = 'none'; // Hide previous errors
                    editProyectoErrorDiv.textContent   = '';

                    // The data-bs-toggle and data-bs-target attributes on the button handle showing the modal
                    // If they weren't there, you'd call: editProyectoModal.show();
                }
<?php #endregion JS AJAX - Edit proyecto ?>
            });
        }

        // --- Handle Edit Form Submission (AJAX) ---
		<?php #region region JS AJAX - Edit Form Submission ?>
        if (editProyectoForm) {
            editProyectoForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Stop default form submission
                editProyectoErrorDiv.style.display = 'none'; // Hide error div initially

                const formData  = new FormData(editProyectoForm);
                const proyectoId    = formData.get('proyectoId');
                const newDescripcion = formData.get('descripcion').trim(); // Get trimmed descripcion

                // Basic client-side validation
                if (!newDescripcion) {
                    editProyectoErrorDiv.textContent   = 'La descripción no puede estar vacía.';
                    editProyectoErrorDiv.style.display = 'block';
                    return; // Stop submission
                }

                // Disable submit button during request? (Optional)
                const submitButton    = editProyectoForm.querySelector('button[type="submit"]');
                submitButton.disabled = true;

                fetch('lproyectos', { // Post to the same controller page
                    method: 'POST',
                    body  : formData // FormData handles content type automatically
                })
                    .then(response => {
                        // Check if response is ok (status 200-299) AND is JSON
                        if (!response.ok) {
                            // Try to parse error from JSON, otherwise use status text
                            return response.json().then(errData => {
                                throw new Error(errData.message || `Error ${response.status}: ${response.statusText}`);
                            }).catch(() => {
                                // If response wasn't JSON or parsing failed
                                throw new Error(`Error ${response.status}: ${response.statusText}`);
                            });
                        }
                        return response.json(); // Parse JSON body
                    })
                    .then(data => {
                        if (data.success) {
                            editProyectoModal.hide(); // Close modal on success

                            // Update the descripcion in the table row directly
                            const tableRow = document.querySelector(`#proyecto-table-body tr[data-proyecto-id="${proyectoId}"]`);
                            if (tableRow) {
                                // Find the cell with the specific class within that row
                                const descripcionCell = tableRow.querySelector('.proyecto-descripcion-cell');
                                if (descripcionCell) {
                                    descripcionCell.textContent = newDescripcion; // Update cell text
                                }
                                // Also update the data-descripcion attribute on the edit button for next time
                                const editButton = tableRow.querySelector('.btn-edit-proyecto');
                                if (editButton) {
                                    editButton.dataset.descripcion = newDescripcion;
                                }
                            }

                            showSweetAlertSuccess('Éxito', 'Descripción actualizada correctamente.');

                        } else {
                            // Show error message inside the modal
                            editProyectoErrorDiv.textContent   = data.message || 'Ocurrió un error al guardar.';
                            editProyectoErrorDiv.style.display = 'block';
                        }
                    })
                    .catch(error => {
                        console.error('Error updating proyecto descripcion:', error);
                        // Show error message inside the modal
                        editProyectoErrorDiv.textContent   = 'Error de red o del servidor: ' + error.message;
                        editProyectoErrorDiv.style.display = 'block';
                    })
                    .finally(() => {
                        // Re-enable submit button
                        submitButton.disabled = false;
                    });
            });
        }
		<?php #endregion JS AJAX - Edit Form Submission ?>
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
